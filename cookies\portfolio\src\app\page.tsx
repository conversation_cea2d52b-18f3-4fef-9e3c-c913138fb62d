import Link from "next/link";
import Image from "next/image";
import { ThemeToggle } from "@/components/theme-toggle";

// GitHub Icon Component
const GitHubIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 0C5.374 0 0 5.373 0 12 0 17.302 3.438 21.8 8.207 23.387c.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23A11.509 11.509 0 0112 5.803c1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576C20.566 21.797 24 17.3 24 12c0-6.627-5.373-12-12-12z"/>
  </svg>
);

// X (Twitter) Icon Component
const XIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
  </svg>
);

// LinkedIn Icon Component
const LinkedInIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
  </svg>
);

// Instagram Icon Component
const InstagramIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
  </svg>
);

export default function Home() {
  return (
    <div className="min-h-screen bg-light-almond dark:bg-dark-bg transition-colors">
      {/* Navigation */}
      <nav className="flex items-center justify-between px-8 py-6 lg:px-16">
        {/* Logo */}
        <div className="text-deep-charcoal dark:text-dark-text">
          <div className="w-8 h-8 bg-deep-charcoal dark:bg-dark-text rounded-full flex items-center justify-center">
            <div className="w-4 h-4 bg-light-almond dark:bg-dark-bg rounded-full"></div>
          </div>
        </div>

        {/* Navigation Links */}
        <div className="hidden md:flex items-center space-x-8 text-deep-charcoal dark:text-dark-text">
          <Link href="#about" className="hover:text-accent-green transition-colors">
            About
          </Link>
          <Link href="#projects" className="hover:text-accent-green transition-colors">
            Projects
          </Link>
          <Link href="#blog" className="hover:text-accent-green transition-colors">
            Blog
          </Link>
          <Link href="#photos" className="hover:text-accent-green transition-colors">
            Photos
          </Link>
        </div>

        {/* Theme Toggle */}
        <ThemeToggle />
      </nav>

      {/* Hero Section */}
      <main className="px-8 lg:px-16 py-16 lg:py-24">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <div className="space-y-8">
            <div className="space-y-6">
              <h1 className="text-4xl lg:text-6xl font-bold text-deep-charcoal dark:text-dark-text leading-tight">
                Software engineer, technical writer
                <br />
                <span className="text-deep-charcoal dark:text-dark-text">& open-source maintainer</span>
              </h1>

              <p className="text-lg text-deep-charcoal/80 dark:text-dark-text/80 max-w-lg leading-relaxed">
                I'm Sumeet Vishwakarma, an experienced Full Stack Engineer passionate about learning
                and building open-source software that is beneficial to developers and the world at large.
              </p>
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-6">
              <a
                href="https://github.com"
                className="flex items-center space-x-2 text-deep-charcoal dark:text-dark-text hover:text-accent-green transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                <GitHubIcon />
                <span>GitHub</span>
              </a>

              <a
                href="https://twitter.com"
                className="flex items-center space-x-2 text-deep-charcoal dark:text-dark-text hover:text-accent-green transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                <XIcon />
                <span>X</span>
              </a>

              <a
                href="https://linkedin.com"
                className="flex items-center space-x-2 text-deep-charcoal dark:text-dark-text hover:text-accent-green transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                <LinkedInIcon />
                <span>LinkedIn</span>
              </a>

              <a
                href="https://instagram.com"
                className="flex items-center space-x-2 text-deep-charcoal dark:text-dark-text hover:text-accent-green transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                <InstagramIcon />
                <span>Instagram</span>
              </a>
            </div>
          </div>

          {/* Right Content - Character Illustration */}
          <div className="relative flex justify-center lg:justify-end">
            <div className="w-80 h-96 lg:w-96 lg:h-[500px] relative">
              <Image
                src="/character-illustration.svg"
                alt="Character illustration showing a person with glasses making a peace sign"
                fill
                className="object-contain"
                priority
              />
            </div>
          </div>
        </div>

        {/* Who Am I Section */}
        <div className="mt-24">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Content - About Text */}
            <div className="space-y-6">
              <div className="relative">
                <h2 className="text-4xl lg:text-5xl font-bold text-deep-charcoal dark:text-dark-text mb-8">
                  Who Am I?
                </h2>
                {/* Character image positioned on the text */}
                <div className="absolute -top-4 -right-8 lg:-right-16 w-24 h-24 lg:w-32 lg:h-32">
                  <Image
                    src="/whoami-character.png"
                    alt="Character with cat illustration"
                    fill
                    className="object-contain"
                  />
                </div>
              </div>

              <div className="space-y-4 text-deep-charcoal/80 dark:text-dark-text/80 leading-relaxed">
                <p className="text-lg">
                  I'm a passionate Full Stack Engineer with a love for creating meaningful software solutions.
                  When I'm not coding, you'll find me exploring new technologies, contributing to open-source projects,
                  or spending quality time with my furry companion.
                </p>

                <p className="text-lg">
                  My journey in software development started with curiosity and has evolved into a mission to build
                  tools that make developers' lives easier and more productive. I believe in the power of clean code,
                  thoughtful design, and collaborative development.
                </p>

                <p className="text-lg">
                  Beyond the technical realm, I enjoy writing about my experiences, sharing knowledge with the
                  community, and mentoring aspiring developers. Every project is an opportunity to learn something
                  new and push the boundaries of what's possible.
                </p>
              </div>

              {/* Skills/Interests */}
              <div className="mt-8">
                <h3 className="text-xl font-semibold text-deep-charcoal dark:text-dark-text mb-4">
                  What I Love Working With
                </h3>
                <div className="flex flex-wrap gap-3">
                  {[
                    'React', 'Next.js', 'TypeScript', 'Node.js',
                    'Python', 'PostgreSQL', 'Docker', 'AWS',
                    'Open Source', 'Technical Writing'
                  ].map((skill) => (
                    <span
                      key={skill}
                      className="px-4 py-2 bg-accent-green/10 text-accent-green rounded-full text-sm font-medium border border-accent-green/20"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Content - Additional Visual Element */}
            <div className="relative">
              <div className="bg-light-almond/50 dark:bg-dark-surface/50 rounded-2xl p-8 lg:p-12">
                <div className="space-y-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-accent-green rounded-full flex items-center justify-center">
                      <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-semibold text-deep-charcoal dark:text-dark-text">Problem Solver</h4>
                      <p className="text-sm text-deep-charcoal/70 dark:text-dark-text/70">
                        I love tackling complex challenges and finding elegant solutions
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-accent-green rounded-full flex items-center justify-center">
                      <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-semibold text-deep-charcoal dark:text-dark-text">Continuous Learner</h4>
                      <p className="text-sm text-deep-charcoal/70 dark:text-dark-text/70">
                        Always exploring new technologies and best practices
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-accent-green rounded-full flex items-center justify-center">
                      <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 8H16c-.8 0-1.54.37-2.01.99L12 11l-1.99-2.01A2.5 2.5 0 0 0 8 8H5.46c-.8 0-1.54.37-2.01.99L1 12v10h2v-6h2.5l-1.5-4.5h2L8 18h8l2-6.5h2L18.5 16H21v6h2z"/>
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-semibold text-deep-charcoal dark:text-dark-text">Team Player</h4>
                      <p className="text-sm text-deep-charcoal/70 dark:text-dark-text/70">
                        Collaboration and knowledge sharing drive the best results
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Projects Section */}
        <div className="mt-24">
          <div className="bg-deep-charcoal dark:bg-dark-surface rounded-2xl p-8 lg:p-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-light-almond dark:text-dark-text mb-12">
              Cool places I worked at
            </h2>

            <div className="space-y-8">
              {/* Zero */}
              <div className="flex items-center justify-between py-4 border-b border-light-almond/10 dark:border-dark-text/10">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-light-almond dark:bg-dark-text rounded-lg flex items-center justify-center">
                    <div className="w-6 h-6 bg-deep-charcoal dark:bg-dark-bg rounded-sm flex items-center justify-center">
                      <div className="w-3 h-3 border-2 border-light-almond dark:border-dark-text rounded-sm"></div>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-semibold text-light-almond dark:text-dark-text">Zero</h3>
                    <p className="text-sm text-light-almond/70 dark:text-dark-text/70">ICT | Software Engineer</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-light-almond/70 dark:text-dark-text/70">June 2025 - Present</p>
                </div>
              </div>

              {/* Google Summer of Code */}
              <div className="flex items-center justify-between py-4 border-b border-light-almond/10 dark:border-dark-text/10">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-lg">G</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-light-almond dark:text-dark-text">Google Summer of Code</h3>
                    <p className="text-sm text-light-almond/70 dark:text-dark-text/70">Contributor under Deepmind</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-light-almond/70 dark:text-dark-text/70">May 2025 - July 2025</p>
                </div>
              </div>

              {/* Cal.com */}
              <div className="flex items-center justify-between py-4 border-b border-light-almond/10 dark:border-dark-text/10">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-800 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">cal</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-light-almond dark:text-dark-text">Cal.com</h3>
                    <p className="text-sm text-light-almond/70 dark:text-dark-text/70">Software Engineer Intern</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-light-almond/70 dark:text-dark-text/70">February 2025 - May 2025</p>
                </div>
              </div>

              {/* Superteam */}
              <div className="flex items-center justify-between py-4">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-light-almond dark:text-dark-text">Superteam, Solana Foundation</h3>
                    <p className="text-sm text-light-almond/70 dark:text-dark-text/70">Member | Grant Recipient</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-light-almond/70 dark:text-dark-text/70">November 2024 - Present</p>
                </div>
              </div>
            </div>

            {/* Education Section */}
            <div className="mt-16">
              <h2 className="text-2xl lg:text-3xl font-bold text-light-almond dark:text-dark-text mb-8">
                Education
              </h2>

              <div className="flex items-center justify-between py-4">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 3L1 9l4 2.18v6L12 21l7-3.82v-6L21 9l-9-6zM18.82 9L12 12.72 5.18 9 12 5.28 18.82 9zM17 15.99l-5 2.73-5-2.73v-3.72L12 15l5-2.73v3.72z"/>
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-light-almond dark:text-dark-text">Mumbai University</h3>
                    <p className="text-sm text-light-almond/70 dark:text-dark-text/70">Electronics & Telecommunication</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-light-almond/70 dark:text-dark-text/70">2022 - 2025 <span className="text-red-400">(Dropped out)</span></p>
                </div>
              </div>
            </div>

            {/* Hire Me Section */}
            <div className="mt-16 pt-8 border-t border-light-almond/10 dark:border-dark-text/10">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                {/* Left - Character Image */}
                <div className="flex justify-center lg:justify-start">
                  <div className="w-64 h-80 lg:w-80 lg:h-96 relative">
                    <Image
                      src="/hire-me-character.png"
                      alt="Character thinking - Why should you hire me?"
                      fill
                      className="object-contain"
                    />
                  </div>
                </div>

                {/* Right - Content */}
                <div className="space-y-6">
                  <h2 className="text-3xl lg:text-4xl font-bold text-light-almond dark:text-dark-text">
                    Why should you hire me?
                  </h2>

                  <div className="space-y-6 text-light-almond/80 dark:text-dark-text/80">
                    <div className="space-y-4">
                      <div className="flex items-start space-x-4">
                        <div className="w-6 h-6 bg-accent-green rounded-full flex items-center justify-center mt-1 flex-shrink-0">
                          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                          </svg>
                        </div>
                        <div>
                          <h3 className="font-semibold text-light-almond dark:text-dark-text mb-2">
                            Full-Stack Expertise
                          </h3>
                          <p className="text-sm leading-relaxed">
                            I bring end-to-end development skills with modern technologies like React, Next.js,
                            Node.js, and cloud platforms. I can handle everything from UI/UX to database design
                            and deployment.
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start space-x-4">
                        <div className="w-6 h-6 bg-accent-green rounded-full flex items-center justify-center mt-1 flex-shrink-0">
                          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                          </svg>
                        </div>
                        <div>
                          <h3 className="font-semibold text-light-almond dark:text-dark-text mb-2">
                            Open Source Contributor
                          </h3>
                          <p className="text-sm leading-relaxed">
                            My contributions to open-source projects demonstrate my ability to work with large
                            codebases, collaborate with global teams, and write maintainable, well-documented code.
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start space-x-4">
                        <div className="w-6 h-6 bg-accent-green rounded-full flex items-center justify-center mt-1 flex-shrink-0">
                          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                          </svg>
                        </div>
                        <div>
                          <h3 className="font-semibold text-light-almond dark:text-dark-text mb-2">
                            Problem-Solving Mindset
                          </h3>
                          <p className="text-sm leading-relaxed">
                            I don't just write code—I solve business problems. I take time to understand
                            requirements, propose solutions, and deliver products that users actually want to use.
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start space-x-4">
                        <div className="w-6 h-6 bg-accent-green rounded-full flex items-center justify-center mt-1 flex-shrink-0">
                          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                          </svg>
                        </div>
                        <div>
                          <h3 className="font-semibold text-light-almond dark:text-dark-text mb-2">
                            Continuous Learning
                          </h3>
                          <p className="text-sm leading-relaxed">
                            Technology evolves fast, and so do I. I stay updated with the latest trends,
                            best practices, and tools to ensure I'm always bringing fresh ideas and
                            efficient solutions to the table.
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Call to Action */}
                    <div className="pt-6">
                      <div className="flex flex-col sm:flex-row gap-4">
                        <button className="px-8 py-3 bg-accent-green hover:bg-accent-green/80 text-white font-medium rounded-lg transition-colors">
                          Let's Work Together
                        </button>
                        <button className="px-8 py-3 border border-light-almond/30 dark:border-dark-text/30 text-light-almond dark:text-dark-text hover:border-accent-green hover:text-accent-green font-medium rounded-lg transition-colors">
                          View My Resume
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Activity Section - Contribution Graph */}
        <div className="mt-24">
          <div className="bg-deep-charcoal dark:bg-dark-surface rounded-2xl p-8 lg:p-12">
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-3xl lg:text-4xl font-bold text-light-almond dark:text-dark-text">
                Contribution Graph
              </h2>
              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <div className="text-sm text-light-almond/70 dark:text-dark-text/70 mb-1">
                    167 contributions in the last year
                  </div>
                  <div className="flex items-center space-x-2 text-xs text-light-almond/50 dark:text-dark-text/50">
                    <span>Less</span>
                    <div className="flex space-x-1">
                      <div className="w-3 h-3 bg-light-almond/20 dark:bg-dark-text/20 rounded-sm"></div>
                      <div className="w-3 h-3 bg-accent-green/30 rounded-sm"></div>
                      <div className="w-3 h-3 bg-accent-green/60 rounded-sm"></div>
                      <div className="w-3 h-3 bg-accent-green rounded-sm"></div>
                    </div>
                    <span>More</span>
                  </div>
                </div>
                <div className="text-right space-y-1">
                  <div className="text-sm font-medium text-accent-green bg-accent-green/20 px-2 py-1 rounded">2025</div>
                  <div className="text-sm text-light-almond/70 dark:text-dark-text/70">2024</div>
                  <div className="text-sm text-light-almond/70 dark:text-dark-text/70">2023</div>
                  <div className="text-sm text-light-almond/70 dark:text-dark-text/70">2022</div>
                  <div className="text-sm text-light-almond/70 dark:text-dark-text/70">2021</div>
                </div>
              </div>
            </div>

            {/* Month Labels */}
            <div className="mb-4">
              <div className="grid grid-cols-12 gap-2 text-xs text-light-almond/50 dark:text-dark-text/50 mb-2">
                <div>Jul</div>
                <div>Aug</div>
                <div>Sep</div>
                <div>Oct</div>
                <div>Nov</div>
                <div>Dec</div>
                <div>Jan</div>
                <div>Feb</div>
                <div>Mar</div>
                <div>Apr</div>
                <div>May</div>
                <div>Jun</div>
              </div>
            </div>

            {/* Contribution Grid */}
            <div className="overflow-x-auto">
              <div className="grid grid-cols-53 gap-1 min-w-[800px]">
                {/* Generate contribution squares for a full year */}
                {Array.from({ length: 371 }, (_, i) => {
                  // Create a more realistic pattern
                  const weekday = i % 7;
                  const isWeekend = weekday === 0 || weekday === 6;
                  const monthProgress = (i / 371) * 12;

                  // Higher activity in certain periods
                  const isHighActivityPeriod =
                    (monthProgress >= 2 && monthProgress <= 4) || // Mar-May (high activity)
                    (monthProgress >= 8 && monthProgress <= 10);  // Sep-Nov (high activity)

                  let intensity = 0;
                  const random = Math.random();

                  if (isWeekend) {
                    // Lower activity on weekends
                    if (random > 0.8) intensity = 1;
                    else if (random > 0.9) intensity = 2;
                  } else if (isHighActivityPeriod) {
                    // Higher activity during active periods
                    if (random > 0.3) intensity = 1;
                    if (random > 0.6) intensity = 2;
                    if (random > 0.8) intensity = 3;
                    if (random > 0.95) intensity = 4;
                  } else {
                    // Normal activity
                    if (random > 0.5) intensity = 1;
                    if (random > 0.75) intensity = 2;
                    if (random > 0.9) intensity = 3;
                  }

                  const getIntensityClass = (level: number) => {
                    switch (level) {
                      case 0: return 'bg-light-almond/20 dark:bg-dark-text/20';
                      case 1: return 'bg-accent-green/30';
                      case 2: return 'bg-accent-green/60';
                      case 3: return 'bg-accent-green/80';
                      case 4: return 'bg-accent-green';
                      default: return 'bg-light-almond/20 dark:bg-dark-text/20';
                    }
                  };

                  return (
                    <div
                      key={i}
                      className={`w-3 h-3 rounded-sm ${getIntensityClass(intensity)} hover:ring-1 hover:ring-accent-green transition-all cursor-pointer`}
                      title={`${intensity} contributions on day ${i + 1}`}
                    />
                  );
                })}
              </div>
            </div>

            {/* GitHub Link */}
            <div className="mt-8 pt-6 border-t border-light-almond/10 dark:border-dark-text/10">
              <a
                href="https://github.com/sumeetvishwakarma"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center space-x-2 text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                </svg>
                <span>View on GitHub</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-deep-charcoal dark:bg-dark-surface border-t border-deep-charcoal/10 dark:border-dark-text/10">
        <div className="px-8 lg:px-16 py-12">
          <div className="grid lg:grid-cols-4 gap-8 lg:gap-12">
            {/* Brand Section */}
            <div className="lg:col-span-2 space-y-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-light-almond dark:bg-dark-text rounded-full flex items-center justify-center">
                  <div className="w-5 h-5 bg-deep-charcoal dark:bg-dark-bg rounded-full"></div>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-light-almond dark:text-dark-text">
                    Sumeet Vishwakarma
                  </h3>
                  <p className="text-sm text-light-almond/70 dark:text-dark-text/70">
                    Software Engineer & Open Source Maintainer
                  </p>
                </div>
              </div>
              <p className="text-light-almond/80 dark:text-dark-text/80 max-w-md leading-relaxed">
                Passionate about building innovative solutions and contributing to the open-source community.
                Let's create something amazing together.
              </p>

              {/* Social Links */}
              <div className="flex items-center space-x-4">
                <a
                  href="https://github.com/sumeetvishwakarma"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-light-almond/10 dark:bg-dark-text/10 hover:bg-accent-green/20 rounded-lg flex items-center justify-center transition-colors group"
                  aria-label="GitHub"
                >
                  <GitHubIcon />
                </a>
                <a
                  href="https://linkedin.com/in/sumeetvishwakarma"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-light-almond/10 dark:bg-dark-text/10 hover:bg-accent-green/20 rounded-lg flex items-center justify-center transition-colors group"
                  aria-label="LinkedIn"
                >
                  <svg className="w-5 h-5 text-light-almond dark:text-dark-text group-hover:text-accent-green transition-colors" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </a>
                <a
                  href="https://twitter.com/sumeetvishwakarma"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-light-almond/10 dark:bg-dark-text/10 hover:bg-accent-green/20 rounded-lg flex items-center justify-center transition-colors group"
                  aria-label="Twitter"
                >
                  <svg className="w-5 h-5 text-light-almond dark:text-dark-text group-hover:text-accent-green transition-colors" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                  </svg>
                </a>
                <a
                  href="mailto:<EMAIL>"
                  className="w-10 h-10 bg-light-almond/10 dark:bg-dark-text/10 hover:bg-accent-green/20 rounded-lg flex items-center justify-center transition-colors group"
                  aria-label="Email"
                >
                  <svg className="w-5 h-5 text-light-almond dark:text-dark-text group-hover:text-accent-green transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </a>
              </div>
            </div>

            {/* Quick Links */}
            <div className="space-y-6">
              <h4 className="text-lg font-semibold text-light-almond dark:text-dark-text">
                Quick Links
              </h4>
              <nav className="space-y-3">
                <Link href="#about" className="block text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors">
                  About Me
                </Link>
                <Link href="#projects" className="block text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors">
                  Projects
                </Link>
                <Link href="#blog" className="block text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors">
                  Blog
                </Link>
                <Link href="#photos" className="block text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors">
                  Photos
                </Link>
                <Link href="/resume" className="block text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors">
                  Resume
                </Link>
              </nav>
            </div>

            {/* Contact Info */}
            <div className="space-y-6">
              <h4 className="text-lg font-semibold text-light-almond dark:text-dark-text">
                Get In Touch
              </h4>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-accent-green/20 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-accent-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm text-light-almond/70 dark:text-dark-text/70">Email</p>
                    <a href="mailto:<EMAIL>" className="text-light-almond dark:text-dark-text hover:text-accent-green transition-colors">
                      <EMAIL>
                    </a>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-accent-green/20 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-accent-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm text-light-almond/70 dark:text-dark-text/70">Location</p>
                    <p className="text-light-almond dark:text-dark-text">
                      India
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-accent-green/20 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-accent-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm text-light-almond/70 dark:text-dark-text/70">Availability</p>
                    <p className="text-light-almond dark:text-dark-text">
                      Open to opportunities
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="mt-12 pt-8 border-t border-light-almond/10 dark:border-dark-text/10">
            <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
              <div className="flex items-center space-x-6 text-sm text-light-almond/70 dark:text-dark-text/70">
                <p>&copy; 2025 Sumeet Vishwakarma. All rights reserved.</p>
                <span className="hidden md:block">•</span>
                <p className="hidden md:block">Built with Next.js & Tailwind CSS</p>
              </div>

              <div className="flex items-center space-x-4 text-sm">
                <Link href="/privacy" className="text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors">
                  Privacy
                </Link>
                <span className="text-light-almond/30 dark:text-dark-text/30">•</span>
                <Link href="/terms" className="text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors">
                  Terms
                </Link>
                <span className="text-light-almond/30 dark:text-dark-text/30">•</span>
                <button
                  onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                  className="text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors flex items-center space-x-1"
                >
                  <span>Back to top</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

import Link from "next/link";

export function FooterBottom() {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="mt-12 pt-8 border-t border-light-almond/10 dark:border-dark-text/10">
      <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
        <div className="flex items-center space-x-6 text-sm text-light-almond/70 dark:text-dark-text/70">
          <p>&copy; 2025 Sumeet Vishwakarma. All rights reserved.</p>
          <span className="hidden md:block">•</span>
          <p className="hidden md:block">Built with Next.js & Tailwind CSS</p>
        </div>
        
        <div className="flex items-center space-x-4 text-sm">
          <Link 
            href="/privacy" 
            className="text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors"
          >
            Privacy
          </Link>
          <span className="text-light-almond/30 dark:text-dark-text/30">•</span>
          <Link 
            href="/terms" 
            className="text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors"
          >
            Terms
          </Link>
          <span className="text-light-almond/30 dark:text-dark-text/30">•</span>
          <button 
            onClick={scrollToTop}
            className="text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors flex items-center space-x-1"
          >
            <span>Back to top</span>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}

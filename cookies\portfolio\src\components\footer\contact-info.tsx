export function ContactInfo() {
  return (
    <div className="space-y-6">
      <h4 className="text-lg font-semibold text-light-almond dark:text-dark-text">
        Get In Touch
      </h4>
      <div className="space-y-4">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-accent-green/20 rounded-lg flex items-center justify-center">
            <svg className="w-4 h-4 text-accent-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <div>
            <p className="text-sm text-light-almond/70 dark:text-dark-text/70">Email</p>
            <a 
              href="mailto:<EMAIL>" 
              className="text-light-almond dark:text-dark-text hover:text-accent-green transition-colors"
            >
              <EMAIL>
            </a>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-accent-green/20 rounded-lg flex items-center justify-center">
            <svg className="w-4 h-4 text-accent-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
          <div>
            <p className="text-sm text-light-almond/70 dark:text-dark-text/70">Location</p>
            <p className="text-light-almond dark:text-dark-text">
              India
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-accent-green/20 rounded-lg flex items-center justify-center">
            <svg className="w-4 h-4 text-accent-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <p className="text-sm text-light-almond/70 dark:text-dark-text/70">Availability</p>
            <p className="text-light-almond dark:text-dark-text">
              Open to opportunities
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
